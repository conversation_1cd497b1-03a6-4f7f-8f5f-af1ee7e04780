<template>
  <view class="skill-black-room">
    <view class="banner"></view>
    <view class="switch">
      <view
        class="switch-item"
        v-for="(item, idx) in profession"
        :key="idx"
        :class="{ actived: currentIdx == idx }"
        @click="handleProfession(idx, item.id)"
      >
        {{ item.name }}
      </view>
    </view>
    <view class="module module1">
      <view class="top">
        <view class="left-title">免费直播</view>
        <view class="right-title"> 技能考前 关键一课 </view>
      </view>
      <view class="center">
        <view class="video">
          <swiper
            class="swiper swiper1"
            circular
            :autoplay="autoplay"
            :interval="interval"
            :duration="duration"
          >
            <swiper-item
              class="swiper-item-box"
              v-for="(item, idx) in allData.free_live"
              :key="idx"
            >
              <view class="video-item">
                <view class="video-image">
                  <image
                    :src="'https://www.jinyingjie.com' + item.image"
                    mode="widthFix"
                  ></image>
                </view>
                <view class="video-name">{{ item.name }}</view>
                <view class="video-teacher"
                  >主讲老师：{{ item.teacher_name || '牙开心讲师' }}</view
                >
                <view class="actions">
                  <view class="left">
                    <view class="time">
                      直播时间: <br />{{ item.bstart_time }}
                    </view>
                    <view class="type" v-if="item.zb_status == 0">未开始</view>
                    <view class="type" v-else-if="item.zb_status == 1">
                      直播中
                    </view>
                    <view class="type" v-else-if="item.zb_status == 2">
                      已结束
                    </view>
                  </view>
                  <login
                    v-if="item.yu_yue_status == 0"
                    :cb="orderMessage"
                    @success="yuyueSuccess(item, idx)"
                  >
                    <view class="right"> 直播预约 </view>
                  </login>
                  <view class="right over" v-else @click="goVideo(item)">
                    已预约
                  </view>
                </view>
              </view>
            </swiper-item>
          </swiper>
        </view>
      </view>
      <image
        class="bottom"
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/8417174572269467658697_bottom.png"
      ></image>
    </view>

    <view class="module module2">
      <view class="top" style="height: 150rpx; background-size: 100% 100%">
        <view class="left-title" style="line-height: 120rpx">技能小黑屋</view>
        <view class="right-title" style="font-size: 18rpx">
          <view style="height: 30rpx"></view>
          <view class="small-title"
            >开班时间：<text>6</text>月(以所在校区开班时间为准)</view
          >
          <view class="small-title">授课方式：<text>现场面授</text></view>
        </view>
      </view>
      <view class="center">
        <view class="info">
          <view
            class="info-item"
            v-for="(item, idx) in xiaoheiwuData[professionId][0].info"
            :key="idx"
          >
            <view class="ok"></view>{{ item }}
          </view>
        </view>
        <view class="image2">
          <swiper
            class="swiper swiper2"
            circular
            :indicator-dots="indicatorDots"
            :autoplay="autoplay"
            :interval="interval"
            :duration="duration"
          >
            <swiper-item
              class="swiper-image2"
              v-for="(item, idx) in xiaoheiwuData[professionId][0].image"
              :key="idx"
            >
              <image :src="item" class="image2-item"></image>
            </swiper-item>
          </swiper>
        </view>
        <view class="details">
          <view class="prize"
            ><text class="text">¥</text
            >{{ xiaoheiwuData[professionId][0].price }}</view
          >
          <login v-if="!yuyueType" @success="xiaoheiwuYuyue">
            <view class="btn"> 小黑屋预约 </view>
          </login>

          <view class="btn over" v-else>已预约</view>
        </view>
      </view>
      <image
        class="bottom"
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/8417174572269467658697_bottom.png"
      ></image>
    </view>

    <view class="module module3">
      <view class="top">
        <view class="left-title">师资护航</view>
        <view class="right-title"> 实力师资护航 · 好课就是不一样 </view>
      </view>
      <view class="center">
        <!-- <view class="teacher" v-if="professionId == '45'">
          <view class="teacher-item">
            <view class="teacher-bj">
              <image
                src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/7305174675939759555790_quanke.png"
                class="teacher-image"
              ></image>
            </view>
            <view class="info" style="margin-top: 0">
              <view class="content">
                牙开心医学致力于打造医学人终生学习和服务平台，将优秀的教学教研内容、有效的课程体系和以人为本的服务结合在一起，聚焦学员个体需求，帮助和成就学员。老师们围绕学员学习规律和学习习惯、学习需求，进行教学教研；本着教育的初心，全方位为口腔医学人员服务，赢得了广大学员的爱戴和好评。
              </view>
            </view>
          </view>
        </view> -->
        <view class="teacher">
          <view class="teacher-item">
            <view class="teacher-bj">
              <image :src="currentTeacher.image" class="teacher-image"></image>
            </view>
            <view class="info">
              <view class="name" v-if="currentTeacher.name">
                <view class="name2">讲师</view>
                {{ currentTeacher.name }}
              </view>
              <view class="title" v-if="currentTeacher.decs1">
                {{ currentTeacher.decs1 }}
              </view>
              <view class="content" v-html="currentTeacher.decs2"> </view>
            </view>
          </view>
          <view class="teacher-boxs">
            <view
              class="teacher-box"
              :class="{ actived: idx == currentTeacherIdx }"
              @click="handleTeacher(professionId, idx)"
              v-for="(item, idx) in teacherData[professionId]"
              :key="idx"
            >
              <image :src="item.image"></image>
            </view>
          </view>
        </view>
      </view>
      <image
        class="bottom"
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/8417174572269467658697_bottom.png"
      ></image>
    </view>

    <view class="module module4">
      <view class="top">
        <view class="left-title">学员评价</view>
        <view class="right-title"> 往年学员的真实评价 </view>
      </view>
      <view class="center">
        <ls-swiper
          :list="evaluationData[professionId]"
          imgKey="imgUrl"
          :crown="true"
          :loop="true"
          :shadow="true"
          height="400"
          previousMargin="120"
          nextMargin="120"
          imgRadius="5"
        >
        </ls-swiper>
      </view>
      <image
        class="bottom"
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/8417174572269467658697_bottom.png"
      ></image>
    </view>

    <view class="module module5">
      <view class="top">
        <view class="left-title">学员故事</view>
        <view class="right-title"> 感恩有你一路相伴 </view>
      </view>
      <view class="center">
        <!-- <view class="news">
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/62e1174573927572741422_banner1%402x.png"
            class="news-item"
          ></image>
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/datatpls/banner2%402x.png"
            class="news-item"
          ></image>
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/datatpls/banner3%402x.png"
            class="news-item"
          ></image>
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/datatpls/banner4%402x.png"
            class="news-item"
          ></image>
        </view> -->
        <view class="news-content" :class="{ active: iszk }">
          <!-- 牙开心医学，是国内知名的医学类综合教育机构，成立于2006年，业务涵盖执业资格、技术实操、继续教育、职称、考研、学历提升等医学领域；旗下拥有医学类图书研发及销售公司、三十二大高端教学基地、在线教育公司、题库公司及牙开心医学教育研究院、直播学院、高端学院、商学院、教育投资基金公司。
          牙开心拥有数百万线上用户，上百家分校、上千家战略合作伙伴、数千名独家师资及员工。历经十余年的发展，已发展成为集线上、线下、图书为一体的综合性医学服务机构及国内知名的医学高端品牌，致力于打造医学人终身学习平台。
          牙开心坚持“学员至上”的理念，始终把“产品驱动、服务驱动、营销驱动、
          数据驱动”作为发展的宗旨。 -->
          <view v-html="storys[professionId]"></view>
        </view>
        <view class="btnzk" :class="{ active: iszk }" @click="iszk = !iszk">
          <text> {{ iszk ? '收起' : '展开' }} </text>
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/9626174676075823889931_xialka.png"
          ></image>
        </view>
      </view>
      <image
        class="bottom"
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/8417174572269467658697_bottom.png"
      ></image>
    </view>

    <view style="height: 50rpx"></view>

    <view class="code">
      <view class="code-item">
        <image
          src="https://www.jinyingjie.com/Public/zhuanti/2024yishibeikao/home/<USER>/weixin.png"
        ></image>
        <view class="name">微信关注</view>
        <view class="name2">第一手新鲜资讯 了如指掌</view>
      </view>
      <view class="code-item">
        <image
          src="https://www.jinyingjie.com/Public/zhuanti/2024yishibeikao/home/<USER>/weixin.png"
        ></image>
        <view class="name">移动官网</view>
        <view class="name2">免下载的在线授课平台</view>
      </view>
      <view class="code-item">
        <image
          src="https://www.jinyingjie.com/Public/zhuanti/2024yishibeikao/home/<USER>/weixin.png"
        ></image>
        <view class="name">APP下载</view>
        <view class="name2">网课、直播课随心学</view>
      </view>
      <view class="code-item">
        <image
          src="https://www.jinyingjie.com/Public/zhuanti/2024yishibeikao/home/<USER>/weixin.png"
        ></image>
        <view class="name">牙开心APP</view>
        <view class="name2">百万牙开心甄选题与知识点</view>
      </view>
    </view>

    <view class="advert">
      <view class="text">技能小黑屋 预约送海量题</view>
      <login v-if="!yuyueType" @success="xiaoheiwuYuyue">
        <!-- @click="xiaoheiwuYuyue" -->
        <view class="btn"> 一键预约 </view>
      </login>
      <view class="btn over" v-else>已预约</view>
    </view>

    <view class="dialog" v-if="cityType">
      <view class="main">
        <view class="title"> 选择校区 </view>
        <view class="citys">
          <view
            class="city-item"
            v-for="(item, idx) in cityData"
            :key="idx"
            :class="{ actived: idx == cityIdx }"
            @click="handleCity(item, idx)"
          >
            {{ item }}
          </view>
        </view>
        <view class="submit" @click="submit1">确认</view>
      </view>
    </view>
    <web-view class="h5" v-if="mType" :src="webViewUrl"></web-view>
  </view>
</template>

<script>
import { goToLogin } from '../../utils/index'
import lsSwiper from '../../components/ls-swiper/index.vue'
import { cityRemark } from '../../api/index'
import login from '../../components/commen/login.vue'
export default {
  name: 'skillBlackRoom',
  components: {
    lsSwiper,
    login
  },
  data() {
    return {
      iszk: false,
      indicatorDots: true,
      autoplay: true,
      interval: 5000,
      duration: 1000,
      currentIdx: 0,
      professionId: 45,
      profession: [
        { name: '口腔医师', id: 45 },
        { name: '临床医师', id: 42 },
        { name: '中医医师', id: 43 },
        { name: '中西医医师', id: 44 }
      ],
      storys: {
        45: `
            <p>
              程云皓同学五年前在朋友的强烈推荐下，选择了牙开心的口腔助理医师王牌联盟课程进行学习。在老师的专业指导下，他认真地夯实了基础知识并锤炼了专业技能，当年便一次性通过了口腔助理医师的考试。经过五年的不断学习与沉淀，程云皓终于满足了执业医师的报名条件。他重新回到牙开心，跟随老师们再次深入备考，最终以456分的成绩通过了执业医师的笔试。
            </p>
            <p>
              以下是我们的采访实录——
            </p>
            <p>
              <strong>在牙开心通过了哪些考试</strong><strong></strong><br />
            我在牙开心参加了口腔助理医师考试和口腔执业医师考试，两次考试都是一次通过的。<br />
            <strong>选择学医的原因</strong><br />
            学医起先是家里的安排，后来学习了种植跟正畸技术以后，慢慢喜欢上了这个职业。因为你可以快速给病人种上植体，尤其是老年人，可以帮助他们解决吃饭问题，正畸的话则可以通过矫正牙齿，帮别人提升颜值。<br />
            <strong>印象深刻的病人</strong><br />
            有一位老大爷面临着全口无牙的窘境，整个嘴巴都是凹的。我们通过全程导板式种植给他改变了状况。我们根据他的骨量，选择即刻负重、当天种牙，那一天很辛苦，但是很值得。后面经过三个月的过渡期，我们又给他进行了马龙桥架的修复，他现在全口义齿，日常生活方便了很多。<br />
            <strong>直播课和面授课的区别</strong><br />
            直播课是你自己对着屏幕上课，面授课是跟老师面对面上课。你看着屏幕学习容易犯困，但面授课的话，大家都在学习，整个氛围能带动你去一起跟着学，融进去。你跟着老师的思维，不会走神，这一点很重要，因为医学知识非常系统，环环相扣，你中间哪一环没学扎实，后期很有可能就跟不上了。因此，面授课的效果要远远大于直播课的效果，如果大家能抽出时间，尽量选择面授课吧。<br />
            <strong>牙开心的上课氛围</strong><br />
            我觉得非常好，大家都挺爱学习的，能带动你也认真学。大家都是花了挺大代价来的，就都很认真，晚上学到十一二点都不回去睡觉，等12点回到宾馆后我们都还要做一套卷子才睡觉。而后第二天早上8点又继续开始学习，一口气连着学20多天。<br />
            <strong>最喜欢的老师</strong><br />
            我喜欢温桐、郭楠、赵庆乐老师，还有康怀朝和赵博儿老师。因为我上过的课比较多，所以认识的老师也多一点，我都很喜欢他们。像老赵他的语言比较犀利，他能更好地帮你分析你当下的学习状态和学习阶段。然后温桐老师有好多秘诀，我们叫做温氏秘诀，你可以很轻松地掌握相关的知识点。郭楠跟赵博儿两位老师长得很漂亮，上课也比较风趣，大家都爱上她们的课。而康怀潮老师，他的种植技术非常厉害。<br />
            <strong>如何评价学管老师</strong><br />
            郭温倩老师、温娟老师还有武淑婷老师她们都非常负责，为我们上课提供了很大的保障。晚上我们练习技能练到很晚，武老师都会陪着我们一起练，有时甚至练到了半夜一两点，她还会一直陪着我们。学管老师们对于我们后勤上的保障真的都非常给力。<br />
            <strong>学霸秘诀</strong><br />
            这次考试我考了456分，我的秘诀就是一定要跟上老师的课堂。如果你上课犯困了那么就站起来，站到旁边听课。或者搬个小板凳，坐到老师讲台下面听课，老师的声音都比较大，我相信在这听课效果肯定好。然后一定要跟老师积极互动，老师提问你要答积极回答。等老师的做题思维和讲课思维你都掌握了，他说上一句你能接上下一句，就说明你真正学会了。不要管周围人的目光，你只有学会了你才能通过考试。不要觉得不好意思，大声吼出来，念出来背出来，你就学会了。然后大家尽量买点风油精、买点咖啡、红牛之类的慢慢熬吧。<br />
            <strong>会给朋友、同行推荐牙开心吗</strong><br />
            会。就拿今年的题来讲，牙开心押题压得很准，技能基本都是原题。另外，笔试即使押不中原题，你也都能做对，知识点课上都讲过了，这点很厉害。<br />
            <strong>想对老师和工作人员们说</strong><br />
            吴洋老师今年确实是比较辛苦。她真的是做到无微不至了，就跟母亲一样，天天在那督促着你，你不学都不行。你不学她都会去酒店里把你喊醒，让你来听课。然后吃饭方面，毕竟众口难调嘛，她经常帮大家去跟酒店协调，还挺辛苦的。<br />
            <strong>想对学弟学妹们说</strong><br />
            建议大家尽早学。每一节直播课，都不要错过。直播课千万不能耽误，一耽误攒一堆，攒一堆你就更不想听了，等上面授课的时候就很难跟上大家的节奏，你会很慌。所以一定跟好直播课，听完第一遍还要再戴上耳机倍速听一遍，而后尽量做做笔记。刷题的话最好放在后半程的冲刺阶段，前半程还是要以积累知识为主，毕竟你只有真正掌握了知识点，才能找到题目的突破点，做题才不会出错。最后，祝学弟学妹都能一次通过考试，顺利地拿到执业医师资格证，然后也祝牙开心越办越好！<br />
            通过程同学的经历，我们看到学霸的成就也并非一蹴而就，而是基于扎实的努力和持续的奋斗。每张成绩单都见证了他们的辛勤汗水和不懈努力。<br />
            牙开心深知医学之路是一场漫长的马拉松，需要耐心、毅力和持续的进步。因此，我们致力于为医学人提供一个终身学习的平台，研发了行业领先的牙开心OMO产品体系，希望能在医学道路上伴随每一位学员不断成长。每年，我们都很欢迎像程同学这样的老学员回归，他们在牙开心陆续完成了职业晋升、职称提升，或者不断学习新技术，提升自我。无论是新学员还是回归的老学员，牙开心都是英杰学子们知识和技能成长的坚实后盾。让我们一起夯实基础，勇往直前，一步一个脚印，去积累，去沉淀，向着医学梦想不断前进吧！<br />
            </p>
            <p>
              <br />
            </p>
          `,
        42: `
           &nbsp;&nbsp;&nbsp;&nbsp;午休间隙，安素芹寻了个安静的角落，专注地在纸上写写画画，为本次采访做着准备。她今年四十多岁，鬓角微微泛白，腿脚不太便利，尽管已学习了整个上午，但依然神采奕奕。在采访过程中，谈及面授课期间的学习生活，她诚恳地表示，一路走来，她得到了众多师长与同学的关心与帮助，对此她深表感激。在讲述的过程中，她对这一段学习生活的珍惜溢于言表，说到动情处潸然泪下，让人为之动容。<br />
          医考成绩公布后，在山东校区金友会的优秀学员表彰环节中，我们再次看到了她的身影。她用辛勤的汗水和不懈的努力赢得了优异的成绩和荣誉。我们深信，在未来的日子里，安素芹医生将会以更加精湛的医术，为广大患者带来更专业的治疗，造福一方百姓。<br />
          以下是我们的采访实录——<br />
          <strong>如何了解到牙开心并决定报名的</strong><br />
          我是通过宣传单了解到牙开心的。在宣传现场，我认识了一位姐姐，她报名牙开心的时候是零基础，没想到一年后她顺利通过了考试，于是第二年我也报名了高端班。<br />
          <strong>选择做医生的原因</strong><br />
          最开始是父母的选择，自己本身并不太喜欢。但当我成为医生后，感受到了大家的尊重。并且，看到病人们在自己的治疗下痊愈了，我觉得挺高兴的，就慢慢喜欢上了这个职业。<br />
          考证目的<br />
          我考证就是为了开诊所，现在开诊所必须要有执业医师证。<br />
          <strong>上课氛围</strong><br />
          挺好的，平时有贾老师她们负责，琐事根本不用我们操心，老师们也都给我们做好了学习规划，我们安心上课就好。我们一般早上7点来上自习课，晚上有时候一起学习到半夜一点或者两点。就像昨晚我学到两点时，还有好多同学还在教室里学习。<br />
          <strong>同学之间的相处</strong><br />
          挺好的，我在班级里朋友比较多。大家做题的时候，谁有疑问都会互相询问，大家慢慢在学习中也就熟悉了起来。刚好我个子比较矮，有辨识度，他们也都容易记住我。大家都是成年人，都知道能拿出这么长一段时间学习不容易，因此大家相处起来也会互相迁就。而且由于我腿脚不太方便，大家都会尽量帮助我，都很照顾我。<br />
          家人对您来牙开心上课的态度<br />
          全家都大力支持我学习。我家小宝上幼儿园，我对象全程负责不用我操心，我家大宝也很贴心，晚上在家里学习时，都会提醒我，妈妈你还有20道题没做呢！<br />
          <strong>最喜欢的老师</strong><br />
          我最喜欢张伟老师、刘越老师、还有刘一燃老师、王文秀老师。他们讲课风格我都挺喜欢的，知识点讲得都很到位，而且他们会分享一些口诀或秘籍，让我们学起来更轻松。其中，刘越老师对我帮助很大，我算是刘越老师的铁杆粉丝吧。还有张伟老师，我哪里不会，都会随时去问他。张老师很好沟通，而且他的课堂不那么严肃，活泼生动，能调动起同学们的积极性，课堂气氛很好。<br />
          <strong>对学管老师的评价</strong><br />
          贾老师很好，她挺照顾我的，还有曾校长，他们都很关心我。在家上网课时，贾老师就经常会问我，姐你上课了吗，做题了吗。上面授课时，如果上课前我没来，贾老师和曾校长就会督促我去上课。平时他们也都会问我最近的学习状态和学习情况，我挺感谢她们的。<br />
          <strong>更喜欢面授课还是直播课</strong><br />
          肯定是面授，因为在家里，很多事情会分散你的注意力，学习时间也不那么集中，面授课要好一些。第一它的氛围好，第二课堂上你轻易不会走神，再加上面授课还有学管老师负责，学习效果会更好一些。<br />
          学习期间最大的挑战<br />
          去年技能考试我没通过，我当时就想放弃了。但是张伟老师跟我聊了很久，一直在鼓励我，我就又坚定了信心，很感谢张伟老师。<br />
          <strong>在牙开心的学习经历对您的影响</strong><br />
          自从走出学校的大门，没想到还能再踏进校园学习，挺感动的。说实话，我挺珍惜这次学习经历的。上次上技能快结课时，我都落泪了，我想这辈子可能就学这么一次了。但这次技能我考过了，我就又来继续学习了，所以我很珍惜这次的学习机会。其实，对像我这个年纪的人而言，学习起来比较吃力，回想起这一段学习历程挺不容易的。<br />
          <strong>想对老师和同学说</strong><br />
          在这段学习旅程中，首先我真的很感谢周围的同学们、我的舍友们，虽然我们才认识这么短的时间，但她们都很照顾我。其次，我很感谢贾老师还有曾校长，是他们一次次在背后推着我前进。另外，我很感谢张伟老师和他带领的各位老师，他们课上认真教学，课后又很关心我的学习情况。我不认真学，都觉得对不起他们的关心。<br />
          <strong>会给朋友或者同行推荐牙开心吗</strong><br />
          会的，因为牙开心是真心帮你提高的医考机构，这里的老师真的很好。我去年认识的几个学员，都是零基础的，他们全都考过了。我将来不管是否从事这个行业，都会介绍学员来牙开心的。<br />
          <strong>想对学弟学妹说</strong><br />
          选医考机构，就选牙开心。这不是给牙开心做宣传，而是我确实觉得牙开心的面授课很好，它让零基础的人也能在好好学习后顺利通过考试。我还希望大家在感觉艰难和痛苦的时候千万别选择放弃。加油，你一定能行，相信自己！最后我想对所有我认识的和帮助过我的人，说一声谢谢！<br />
          自牙开心创办以来，每年都会迎来一批年长的学员。尽管岁月已在他们脸上留下了痕迹，但他们的学习劲头却丝毫不减，依然夜以继日、风雨无阻地“坚守阵地”。也正是他们的存在，不仅为其所在班级注入了更多的活力和正能量，更以其实际行动，激励着每一位学员更加努力地追求自己的梦想。在此，衷心地祝愿所有英杰学员们都能不被年纪限定人生，不被岁月抹去激情，向着自己的理想坚定地走去。<br />
          <br />
        `,
        43: `
          <p>
	&nbsp;&nbsp;&nbsp;&nbsp;几年前，孙悦通过中医治疗，终于摆脱了多年来的顽疾困扰。在那段接受治疗的日子里，她亲眼见证了馆内许多患有疑难杂症的病人痊愈康复，这让她直观地感受到中医的非凡魅力和奥妙之处，由此她对中医产生了浓厚的兴趣。&nbsp;
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;同时，作为一名母亲，她内心深处涌动着一种本能的渴望——若能掌握中医知识，便能更有效地守护家人的健康，还能在自己的能力范围内，为更多的人提供帮助。
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;她参加了牙开心彩虹计划的高端班，并全身心地投入到学习中，心无旁骛地吸收中医知识。通过不懈的努力和勤奋学习，最终成长为一名出色的中医悦，并顺利地通过了中医执业助理医师考试。&nbsp;
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;通过考试后，孙悦到非常惊讶，未曾料想自己能够一次性顺利通过。&nbsp;
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;学习过程中，她看到同学们展现出极大的学习热情，彼此间也相互帮助，相处融洽。鉴于自己的基础知识尚显薄弱，通常会向同学们请教，而他们也总是热情地为自己解答疑惑，让她印象颇深。
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;她说，高端班的课堂学习效率极高。在课堂上紧随老师的思路，特别是大圣老师和孙晓旭老师的课堂上，频繁地进行互动提问使得每堂课都能够牢固地掌握很多知识点。
她说最喜欢大圣老师，大圣的课堂满满的都是干货。只要按照她的教学思路，认真听课，通过医考是没有问题的。她所传授的解题方法极为高效，使学员能够迅速且精确地解答问题。在这次考试中，孙悦说在完成所有题目后，仍有充足的时间。
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;每位学管老师都表现出极高的敬业精神和热情。尤其是高瑞雪老师，面对任何问题，她总是积极主动地为学员提供解决方案。在学习生活中，她也对学员关怀备至。
学习压力确实较为沉重。在参加面授课程之前，孙悦白天需在中医馆工作，晚上则需回家煮饭并照顾孩子，导致学习时间颇为紧张。因此，在面授课程期间，只好拼尽全力，心无旁骛，尽量追赶进度，常常学习至凌晨一点之后才就寝。&nbsp;
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;她说既然已经报名参加了课程，就要遵循老师的教学思路。应当按照教师的指导进行学习，专注于课程内容，紧跟教师步伐，一定避免自行盲目学习。
我认为这样才能确保学习效果，从而获得较高的通过率。&nbsp;
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;最后他衷心感谢牙开心的各位老师。此次以极短的时间顺利通过了考试，真的非常感谢牙开心。&nbsp;
</p>
<p>
	------中医高端班孙悦的采访笔录
</p>
        `,
        44: `
         <p>
	&nbsp;&nbsp;&nbsp;&nbsp;几年前，孙悦通过中医治疗，终于摆脱了多年来的顽疾困扰。在那段接受治疗的日子里，她亲眼见证了馆内许多患有疑难杂症的病人痊愈康复，这让她直观地感受到中医的非凡魅力和奥妙之处，由此她对中医产生了浓厚的兴趣。&nbsp;
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;同时，作为一名母亲，她内心深处涌动着一种本能的渴望——若能掌握中医知识，便能更有效地守护家人的健康，还能在自己的能力范围内，为更多的人提供帮助。
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;她参加了牙开心彩虹计划的高端班，并全身心地投入到学习中，心无旁骛地吸收中医知识。通过不懈的努力和勤奋学习，最终成长为一名出色的中医悦，并顺利地通过了中医执业助理医师考试。&nbsp;
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;通过考试后，孙悦到非常惊讶，未曾料想自己能够一次性顺利通过。&nbsp;
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;学习过程中，她看到同学们展现出极大的学习热情，彼此间也相互帮助，相处融洽。鉴于自己的基础知识尚显薄弱，通常会向同学们请教，而他们也总是热情地为自己解答疑惑，让她印象颇深。
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;她说，高端班的课堂学习效率极高。在课堂上紧随老师的思路，特别是大圣老师和孙晓旭老师的课堂上，频繁地进行互动提问使得每堂课都能够牢固地掌握很多知识点。
她说最喜欢大圣老师，大圣的课堂满满的都是干货。只要按照她的教学思路，认真听课，通过医考是没有问题的。她所传授的解题方法极为高效，使学员能够迅速且精确地解答问题。在这次考试中，孙悦说在完成所有题目后，仍有充足的时间。
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;每位学管老师都表现出极高的敬业精神和热情。尤其是高瑞雪老师，面对任何问题，她总是积极主动地为学员提供解决方案。在学习生活中，她也对学员关怀备至。
学习压力确实较为沉重。在参加面授课程之前，孙悦白天需在中医馆工作，晚上则需回家煮饭并照顾孩子，导致学习时间颇为紧张。因此，在面授课程期间，只好拼尽全力，心无旁骛，尽量追赶进度，常常学习至凌晨一点之后才就寝。&nbsp;
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;她说既然已经报名参加了课程，就要遵循老师的教学思路。应当按照教师的指导进行学习，专注于课程内容，紧跟教师步伐，一定避免自行盲目学习。
我认为这样才能确保学习效果，从而获得较高的通过率。&nbsp;
</p>
<p>
	&nbsp;&nbsp;&nbsp;&nbsp;最后他衷心感谢牙开心的各位老师。此次以极短的时间顺利通过了考试，真的非常感谢牙开心。&nbsp;
</p>
<p>
	------中医高端班孙悦的采访笔录
</p>
        `
      },
      currentTeacherIdx: 0,
      allData: {
        free_live: []
      },
      user_id: '',
      currentTeacher: {
        // image:
        //   'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/zhaoqingle.png',
        // name: '赵庆乐',
        // decs1: '【授课科目】全科',
        // decs2:
        //   '【名师简介】牙开心口腔专业图书的主编，参与学习包全线产品的编写工作。从事口腔医师考试培训多年，对于考试大纲及考点非常熟悉。【授课风格】主讲的课程有口腔主治/执业/助理医师全部课程。授课时激情洋溢，风趣幽默，擅长梳理考点，深入浅出，直击考点、难点，使学生听后如沐春风、茅塞顿开。在轻松的授课氛围中，构建知识框架，把握考试方向与规律，预测性强，直击考点难点，备受学员好评，被誉为“口腔行业里的男神”。'
        image:
          'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/7305174675939759555790_quanke.png',
        name: '',
        decs1: '',
        decs2:
          '牙开心医学致力于打造医学人终生学习和服务平台，将优秀的教学教研内容、有效的课程体系和以人为本的服务结合在一起，聚焦学员个体需求，帮助和成就学员。老师们围绕学员学习规律和学习习惯、学习需求，进行教学教研；本着教育的初心，全方位为口腔医学人员服务，赢得了广大学员的爱戴和好评。'
      },
      teacherData: {
        45: [
          {
            image:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/7305174675939759555790_quanke.png',
            name: '',
            decs1: '',
            decs2:
              '牙开心医学致力于打造医学人终生学习和服务平台，将优秀的教学教研内容、有效的课程体系和以人为本的服务结合在一起，聚焦学员个体需求，帮助和成就学员。老师们围绕学员学习规律和学习习惯、学习需求，进行教学教研；本着教育的初心，全方位为口腔医学人员服务，赢得了广大学员的爱戴和好评。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/zhaoqingle.png',
            name: '赵庆乐',
            decs1: '【授课科目】全科',
            decs2:
              '【名师简介】牙开心口腔专业图书的主编，参与学习包全线产品的编写工作。从事口腔医师考试培训多年，对于考试大纲及考点非常熟悉。【授课风格】主讲的课程有口腔主治/执业/助理医师全部课程。授课时激情洋溢，风趣幽默，擅长梳理考点，深入浅出，直击考点、难点，使学生听后如沐春风、茅塞顿开。在轻松的授课氛围中，构建知识框架，把握考试方向与规律，预测性强，直击考点难点，备受学员好评，被誉为“口腔行业里的男神”。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/haolihui.png',
            name: '郝立辉',
            decs1: '【主讲科目】口解',
            decs2:
              '【老师简介】牙开心口腔专业口解图书的主编，参与学习包口解全线产品的编写工作。多年医考培训经验。资深医学考试辅导教师，从事医学考试类培训多年2015年取得高校讲师职称，2017年取得口腔医学主治医师职称;2011年取得三级心理咨询师资格证，公共营养师资格证。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/yuanyuan.png',
            name: '袁媛',
            decs1: '【主讲科目】修复',
            decs2:
              '【老师简介】口腔执业医师考试培训讲师;10余年医考培训经验【授课风格】善于启发，能够技巧性地让学员掌握知识点，讲解幽默，让枯燥的课程变得生动活泼、通俗易懂，使学员们在紧张的复习过程中能够做到目标明确，直击考试重心;构筑了其广博的知识底蕴和独到的授课风格;'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/zhaozhe.png',
            name: '赵哲',
            decs1: '【主讲科目】口组',
            decs2:
              '【老师简介】石家庄人民医学高等专科学校大学老师，2015年取得口腔资格证【授课风格】一贯坚持化繁为简、融会贯通的学习方法，将大量专业书籍由薄读厚，再由厚读薄，从而构筑了其广博的知识底蕴和独到的授课风格;综合运用表格记忆、对比记忆、口诀记忆等多种方法强化各个重要考点;形式上擅长将枯燥的知识跟常见事物联系起来，力求生动活泼，通俗易懂;'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/guoxiaohua.png',
            name: '郭晓华',
            decs1: '【主讲科目】口内',
            decs2:
              '【老师简介】 牙开心口腔专业口内图书的主编，参与学习包口内全线产品的编写工作。牙开心独家核心讲师2014年取得高校讲师职称，2015年取得口腔主治医师职称;2015年取得执业药师资格证，具备三师资格。【授课风格】善于启发，能够技巧性地让学员掌握知识点，真正做到理解、学会、记牢、做对题;讲解幽默，让枯燥的课程变得生动活泼、通俗易懂，使学员们在紧张的复习过程中能够做到目标明确，直击考试重心;'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/zhangjian.png',
            name: '张健',
            decs1: '【主讲科目】口外',
            decs2:
              '【老师简介】某著名医学高校教师，医考教师，从事医学考试类培训多年;曾在某三甲医院口腔科任职多年，主治医师【授课风格】和学生的互动性较好，善于启发，能够技巧性地让学员掌握知识点，真正做到理解、学会、记牢、做对题;.综合运用表格记忆、对比记忆、口诀记忆等多种方法强化各个重要考点;形式上擅长将枯燥的知识跟常见事物联系起来，力求生动活泼，通俗易懂;'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/lining.png',
            name: '李宁',
            decs1: '【主讲科目】牙体 、牙周、儿牙、黏膜、口腔技能 ',
            decs2:
              '【授课风格】一贯坚持化繁为简、融会贯通的学习方法，将大量专业书籍由薄读厚，再由厚读薄，从而构筑了其广博的知识底蕴和独到的授课风格；综合运用表格记忆、对比记忆、口诀记忆等多种方法强化各个重要考点；形式上擅长将枯燥的知识跟常见事物联系起来，力求生动活泼，通俗易懂；'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/zhaidanni.png',
            name: '翟丹妮',
            decs1: '【主讲科目】口腔颌面外科学、阻生齿拔除入门到落地',
            decs2: `【老师简介】
                                <br/>口腔医学 全科主治医师
                                <br/>大连医科大学 口腔医学学士
                                <br/>中华口腔医学会口腔颌面外科学专委会委员
                                <br/>专长：
                                <br/>1.微创拔牙，尤其各种阻生齿、埋伏牙、复杂牙的拔除
                                <br/>2.唇颊系带修整，牙槽骨修整术
                                <br/>3.口腔颌面外科常见疾病影像学诊断和治疗
                                <br/>4.显微树脂微创修复
                                <br/>教学/工作经验：
                                <br/>1.非洲乍得恩贾梅纳中国医疗队
                                <br/>2.大型连锁口腔医院口外科任职
                                <br/>3.主讲国家执业医师考试口腔科颌面外科学，对考试方向及内容有深入的了解。                               
                                <br/>【授课风格】嗓音洪亮，慷慨激昂，善于调动课堂氛围。运用图片、思维导图、口诀等方法生动地让学员掌握知识点，真正做到理解、学会、记牢、做对题；讲解幽默，让枯燥的课程变得生动活泼、通俗易懂，使学员们在紧张的复习过程中能够做到目标明确，直击考试重心。 
                                `
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/mawenni.png',
            name: '马文妮',
            decs1: '【主讲科目】口腔修复学、口腔预防医学、口腔技能',
            decs2:
              '【名师简介】毕业于中国医科大学口腔医学专业，中华口腔医学会会员，授课经验丰富，有丰富的专业知识及临床工作经验。【授课风格】能够以点带面讲解重点学科难点内容，图文并茂，注重干货。以诙谐幽默的形式加强记忆，运用口诀、表格、形象记忆、手绘简图等形式将知识点讲解透彻，深入浅出。深受广大学员喜爱。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/guoxiaojiao.png',
            name: '郭晓娇',
            decs1: '【主讲科目】口解、口组 ，技能1，2站',
            decs2:
              '【师资老师介绍】西安市某医院口腔科工作多年，善于启发，能够技巧性地让学员掌握知识点，真正做到理解、学会、记牢、做对题；【授课风格】一贯坚持化繁为简、融会贯通的学习方法，将大量专业书籍由薄读厚，再由厚读薄，从而构筑了其广博的知识底蕴和独到的授课风格；'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/dengbing.png',
            name: '邓斌',
            decs1: '【主讲科目】口外、口修、口组、口解、口预、技能',
            decs2:
              '【师资老师介绍】 牙开心独家核心讲师，从事口腔教学临床多年，有丰富的教学经验。【授课风格】喜欢rap，却总是唱不好；喜欢备牙，车针走位又太风骚；曾梦想仗剑走天涯 却因牙髓炎而被迫取消；如今致力于口腔医考，对口腔执业医师考试颇有研究。【干货多、考点准、 简单学】是他对教学的九字原则。快乐学习，开心拿证。看到这就行，赶紧去看书。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/jiaxin.png',
            name: '佳昕',
            decs1: '【主讲科目】口内，技能',
            decs2:
              '【师资老师介绍】牙开心独家核心讲师，专注于医考培训，有多年授课经验，参与牙开心新版图书编写工作【授课风格】多年的授课经验，幽默的教学风格，教学方式灵活多变，一个生动形象的比喻，犹如画龙点睛，给学生开启智慧之门。课堂上讲课层层剖析，环环相扣，用思维的逻辑力量吸引学生的注意力。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/guonan.png',
            name: '郭楠',
            decs1: '【主讲科目】口内、口解',
            decs2:
              '【名师简介】牙开心独家核心讲师，有丰富的教学经验。【授课风格】一贯坚持化繁为简、融会贯通的学习方法，将大量专业书籍由薄读厚，再由厚读薄，从而构筑了其广博的知识底蕴和独到的授课风格；综合运用表格记忆、对比记忆、口诀记忆等多种方法强化各个重要考点；形式上擅长将枯燥的知识跟常见事物联系起来，力求生动活泼，通俗易懂；'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/zhangguoliang.png',
            name: '张国良',
            decs1: '【主讲科目】口腔颌面外科学，技能一、二、四站',
            decs2:
              '【名师简介】牙开心独家核心讲师，临床一次性根管治疗、瓷嵌体修复讲师，主治医师【授课风格】授课风趣幽默，擅长化繁为简，让复杂的课程内容通俗易懂'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/sujing.png',
            name: '苏静',
            decs1: '【主讲科目】口外',
            decs2:
              '【老师简介】牙开心口腔专业口外图书的主编，参与学习包口外全线产品的编写工作。某著名大学老师，资深医学考试辅导教师，10余年医考培训经验。 2008年获得口腔临床主治医师职;2012年获得高校讲师职称。为双师型资质【授课风格】在教学过程中善于启发学生，让学生在充分理解难点后记忆， 有效避免“今天记明天忘的尴尬”，变短时记忆为长久性记忆，学员课堂记忆效率大大提高，课后会做题，做对题。授课条理清晰、逻辑性强，将前后知识融会贯通，对课程整理把握能力强。课程讲解通俗易懂、生动幽默，课堂气氛活跃，能够带动学生跟随老师思路进行学习，课堂学习效率高。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/zhanxing.png',
            name: '詹星',
            decs1: '【主讲科目】牙体 、牙周、儿牙、黏膜、口腔技能',
            decs2:
              '【授课风格】一贯坚持化繁为简、融会贯通的学习方法，将大量专业书籍由薄读厚，再由厚读薄，从而构筑了其广博的知识底蕴和独到的授课风格；综合运用表格记忆、对比记忆、口诀记忆等多种方法强化各个重要考点；形式上擅长将枯燥的知识跟常见事物联系起来，力求生动活泼，通俗易懂；'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/kq/yuanzilu.png',
            name: '元子路',
            decs1: '【主讲科目】口腔颌面外科学',
            decs2:
              '【名师简介】十余年医考培训经验，考点把握精准。【授课风格】擅长启发式教学，有亲和力。授课深入浅出，考点明确，总结精辟。对零基础学院有启蒙意义，又可冲刺阶段学生进行点拨。课堂风趣而富有激情，深受考生爱戴。'
          }
        ],
        42: [
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/lc/zhangwei.png',
            name: '张伟老师',
            decs1: '【主讲科目】临床主治、临床执业、临床助理、执业药师',
            decs2:
              '【名师简介】牙开心临床专业图书的主编，参与学习包全线产品的编写工作。医考培训资深高级讲师、副高级、多主治医师（内科、全科、整形外科）、全科医师、执业药师。2007年参与国家执业医师资格考前培训工作，13年医考辅导经验，培训学员数十万余人。面授课达千余场，直播课时超过1万小时。足迹遍布全国30多省市。张伟老师结合画图、模具、思维导图等多种授课方法，分析20年历年真题结合业界所有名师智慧，总结出一套独特的授课方法；张伟老师秉承“先过关，后提升原则”，紧贴国家新大纲，围绕“考什么？怎么考？怎么记”帮助学生，迅速提升成绩及应试能力！让学生一次过关！张伟老师的授课，课堂气氛异常活跃，让同学们在开心和谐的气氛下学会医考必考点！'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/lc/wangwenxiu.png',
            name: '王文秀',
            decs1:
              '【主讲科目】临床执业助理医师：基础医学综合，人文基础综合，预防医学综合',
            decs2:
              '【名师简介】高校讲师，三甲医院临床医师，有多年教学及临床经验。一贯坚持化繁为简、融会贯通的学习方法，将大量专业书籍由薄读厚，再由厚读薄，从而构筑了其广博的知识底蕴和独到的授课风格；综合运用表格记忆、对比记忆、口诀记忆等多种方法强化各个重要考点；形式上擅长将生理生化药理病理等枯燥知识跟常见事物联系起来，力求生动活泼，通俗易懂；讲解生动、幽默、形象，在紧张的授课过程中能够寓教于乐，做到目标明确，直击考试重心，使学员学习不枯燥。临床经验丰富，善于将知识点与临床紧密结合，使学员顺利通过考试的同时能学到更多的临床知识，对学员认真负责，工作勤奋踏实，深受全国各地考生喜欢，已成为牙开心教育核心授课师资及学员最受欢迎的老师之一。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/lc/yefan.png',
            name: '叶凡',
            decs1: '【主讲科目】运动、风湿免疫、其他系统+技能',
            decs2:
              '【名师简介】从事多年医学考前培训，是一位经验丰富的培训老师，积累了丰富的教学素材与培训经验。对临床医学考试命题规律和应试技巧有着深入理解和研究。叶老师的授课风格严谨，贴近临床，切合考试，讲解透彻，善于总结，通俗易懂，通过一些学习方法让学员在短时间内收获大量的知识信息；把握考试重点脉络，从整体上掌握临床考试的主要考点内容并加以精炼；并通过记忆口诀、对比表格和形象归纳记忆方法，帮助学员轻松理解、灵活运用知识轻松应对考试。叶老师以其精品的授课，得到了广大学员真心认可，成为牙开心最受欢迎的老师之一。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/lc/liuyue.png',
            name: '刘越',
            decs1: '【主讲科目】女性生殖系统、技能',
            decs2:
              '【名师简介】北京牙开心医学教育独家授课老师，多年从事医考培训工作，经验丰富。能精准把握考点，课程深入浅出，擅长将复杂的知识简洁化，条理化，善于总结记忆方法。让学员知道考什么、怎么考、怎么记，让繁重的学习变得轻松有序、有针对性。是一名集专业、辅导、讲师为一体的专业化辅导老师。口头禅是“越努力越幸运”！'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/lc/sunjiayi.png',
            name: '孙佳忆',
            decs1: '【主讲科目】临床执业及助理实践技能、女性生殖系统、儿科系统',
            decs2:
              '【名师简介】妇产科医师、讲师。具有多年的执业医师资格考试培训经验，能精准把握考点，讲课细腻严谨，重点突出，层次分明，思路清晰，语言简练，逻辑性强，对常考点、易错点把握极为精准，让考生做题迅速秒杀，深受广大学员喜爱，是一位实力派讲师，面对医考考生我的信念就是：只有想不到的，没有做不到的！'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/lc/luoyajie.png',
            name: '罗亚杰',
            decs1:
              '【主讲科目】临床执业、临床助理：消化、泌尿、心血管及呼吸系统，技能第一、二、三站。',
            decs2:
              '【名师简介】罗老师从事医学考试培训多年，在培训与教学方面积累了丰富经验。对医学考试命题规律和应试技巧有较深入理解和研究。罗亚杰老师授课风格简洁干练，善于“以讲代考”、“以讲代练”，全程课堂气氛活跃，通过对考点的梳理与归纳，让学员轻松掌握考试的重点及难点。罗亚杰老师以其精彩的授课，得到了广大学员的喜欢与认可。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/lc/liuyiran.png',
            name: '刘一燃',
            decs1:
              '【主讲科目】内科学内分泌，血液和内分泌系统、精神神经系统；实践技能。',
            decs2:
              '【名师简介】注重逻辑推理，理论密切结合病例，深入浅出诠释知识点，帮助学员“听的懂”。善于运用思维导图，场景设计等科学记忆方法，帮助学员“记得住”。善于运用大数据方式深度研究考点和真题，积极把握考试动态及命题方向，帮助学员“答的对”。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/lc/luoyi.png',
            name: '罗逸',
            decs1:
              '【主讲科目】 消化系统  运动系统  乡村全科西医部分  实践技能 ',
            decs2:
              '【名师简介】 一贯坚持化繁为简、 融会贯通的学习方法； 善于站在学生角度看问题， 想学生之想 ，急学生之急。 运用“题眼”“关键词”等技巧让学生学会做题 ，掌握知识点 。真正做到学会病 ，做对题。'
          }
        ],
        43: [
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/zy/anjie.png',
            name: '安杰',
            decs1:
              '牙开心医学核心独家授课老师，秉承用心引导 医考通关的教学理念',
            decs2:
              '【主讲专业】中医主治,中医师承,主管护师,初级护师,护士资格,中西医执业助理医师,中西医执业医师,中医执业助理医师,中医执业医师,临床执业助理医师,临床执业医师'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/zy/nanjingshu.png',
            name: '南静毓',
            decs1:
              '牙开心医学教育研究牙开心医学教育研究针灸学教研室主任，牙开心医学教育独家核心授课教师',
            decs2:
              '【主讲科目】中医技能、中药学、方剂学、针灸学、中医妇科学【老师介绍】针灸学教研室主任。幽默诙谐，激情洋溢，通俗易懂，深入研究了历年考题，对考试重点、难点、考点可很好预测。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/zy/fengxinyu.png',
            name: '冯欣语',
            decs1:
              '牙开心医学教育研究中西医教研室主任，牙开心医学教育独家核心授课教师',
            decs2:
              '【主讲科目】实践技能、中西医结合内科学、中西医结合外科学、中西医结合妇科学、中西医结合儿科学【老师介绍】担任牙开心中西医教研室主任，中西医图书的主编。高校讲师，主持并参与多项省市级科研课题。主讲课程有中西医结合实践技能、中西医结合内外妇儿。冯老师具有十余年教学辅导经验，讲授重点突出，生动形象，独创的以题带点、以点带面的实践技能培训方法，深受学员欢迎。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/zy/sunxiaoxu.png',
            name: '孙晓旭',
            decs1:
              '牙开心医学教育研究西医部分教研室主任，牙开心医学教育独家核心授课教师',
            decs2:
              '【主讲科目】内科学、诊断学基础、药理学、传染病、医学伦理、卫生法规【老师介绍】担任西医部分教研室主任。理智型，讲解深入浅出，条理清楚，层层剖析，环环相扣，论证严密，结构严谨，用思维的逻辑力量吸引学生的注意力，用理智控制课堂教学进程。坚持化繁为简，精简记忆，准确把握学习重点，提高学习效率。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/zy/zhangshengqi.png',
            name: '张圣淇',
            decs1:
              '牙开心医学教育研究院中药学、方剂学教研室主任，牙开心医学教育独家核心授课教师',
            decs2:
              '【主讲科目】中医基础理论、中医诊断学、方剂学、中药学、中医内科学、中医外科学、中医妇科学、中医儿科学。【老师介绍】担任牙开心中药学、方剂学教研室主任。中国民间中医药研究开发协会古乾坤灸分会副秘书长.从事执业药师（中药）、执业医师培训（中医）多年。在授课过程中，始终秉承寓教于乐，拒绝死记硬背，独创了许多趣味记忆方法、应试技巧，真正能够做到在授课中以题带点、知识点举一反三，帮助数万考生顺利通过考试，并真正让学员将所学知识致力于生活工作中去。其独特的教学方法和人格魅力深受广大学员的喜爱。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/zy/xingyue.png',
            name: '馨月',
            decs1: '牙开心医学教育独家核心授课教师',
            decs2:
              '【主讲科目】中医基础理论、中医诊断学、中药学、方剂学、中医内科学、技能操作【老师介绍】参与牙开心中医\中西医图书产品的编写。风格鲜明，富有激情；思路清晰，层次分明；重点突出，通俗易懂，富有应试实战经验。善长运用独特的互动教学法，能快速地调动学员的学习激情使学员在活泼愉悦的氛围中全方位把握考点知识。'
          }
        ],
        44: [
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/zy/anjie.png',
            name: '安杰',
            decs1:
              '牙开心医学核心独家授课老师，秉承用心引导 医考通关的教学理念',
            decs2:
              '【主讲专业】中医主治,中医师承,主管护师,初级护师,护士资格,中西医执业助理医师,中西医执业医师,中医执业助理医师,中医执业医师,临床执业助理医师,临床执业医师'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/zy/nanjingshu.png',
            name: '南静毓',
            decs1:
              '牙开心医学教育研究牙开心医学教育研究针灸学教研室主任，牙开心医学教育独家核心授课教师',
            decs2:
              '【主讲科目】中医技能、中药学、方剂学、针灸学、中医妇科学【老师介绍】针灸学教研室主任。幽默诙谐，激情洋溢，通俗易懂，深入研究了历年考题，对考试重点、难点、考点可很好预测。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/zy/fengxinyu.png',
            name: '冯欣语',
            decs1:
              '牙开心医学教育研究中西医教研室主任，牙开心医学教育独家核心授课教师',
            decs2:
              '【主讲科目】实践技能、中西医结合内科学、中西医结合外科学、中西医结合妇科学、中西医结合儿科学【老师介绍】担任牙开心中西医教研室主任，中西医图书的主编。高校讲师，主持并参与多项省市级科研课题。主讲课程有中西医结合实践技能、中西医结合内外妇儿。冯老师具有十余年教学辅导经验，讲授重点突出，生动形象，独创的以题带点、以点带面的实践技能培训方法，深受学员欢迎。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/zy/sunxiaoxu.png',
            name: '孙晓旭',
            decs1:
              '牙开心医学教育研究西医部分教研室主任，牙开心医学教育独家核心授课教师',
            decs2:
              '【主讲科目】内科学、诊断学基础、药理学、传染病、医学伦理、卫生法规【老师介绍】担任西医部分教研室主任。理智型，讲解深入浅出，条理清楚，层层剖析，环环相扣，论证严密，结构严谨，用思维的逻辑力量吸引学生的注意力，用理智控制课堂教学进程。坚持化繁为简，精简记忆，准确把握学习重点，提高学习效率。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/zy/zhangshengqi.png',
            name: '张圣淇',
            decs1:
              '牙开心医学教育研究院中药学、方剂学教研室主任，牙开心医学教育独家核心授课教师',
            decs2:
              '【主讲科目】中医基础理论、中医诊断学、方剂学、中药学、中医内科学、中医外科学、中医妇科学、中医儿科学。【老师介绍】担任牙开心中药学、方剂学教研室主任。中国民间中医药研究开发协会古乾坤灸分会副秘书长.从事执业药师（中药）、执业医师培训（中医）多年。在授课过程中，始终秉承寓教于乐，拒绝死记硬背，独创了许多趣味记忆方法、应试技巧，真正能够做到在授课中以题带点、知识点举一反三，帮助数万考生顺利通过考试，并真正让学员将所学知识致力于生活工作中去。其独特的教学方法和人格魅力深受广大学员的喜爱。'
          },
          {
            image:
              'https://www.jinyingjie.com/Public/zhuanti/2025skillBlackRoom/teacher/zy/xingyue.png',
            name: '馨月',
            decs1: '牙开心医学教育独家核心授课教师',
            decs2:
              '【主讲科目】中医基础理论、中医诊断学、中药学、方剂学、中医内科学、技能操作【老师介绍】参与牙开心中医\中西医图书产品的编写。风格鲜明，富有激情；思路清晰，层次分明；重点突出，通俗易懂，富有应试实战经验。善长运用独特的互动教学法，能快速地调动学员的学习激情使学员在活泼愉悦的氛围中全方位把握考点知识。'
          }
        ]
      },
      xiaoheiwuData: {
        45: [
          {
            info: [
              '口腔技能小黑屋，1天揭秘答题技巧&解题思路',
              '锻炼各种口腔常见病、多见病的分析能力',
              '以高能紧凑节奏提前适应考试气氛',
              '以各类病种的病例诊断、鉴别以及治疗设计',
              '【病例分析】专项讲解',
              '独家教研好课,百城千场数万学员见证',
              '2018-2025 八年经典再出发'
            ],
            image: [
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/6c6d174590774624235656_kq1.png',
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/136c17459077590464265_kq2.png',
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/2205174590776836123224_kq3.png',
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4db8174590777883684691_kq4.png',
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/300b174590778846276371_kq5.png'
            ],
            price: '99-399'
          }
        ],
        42: [
          {
            info: [
              '临床小黑屋',
              '2天封闭面授开课',
              '囊括技能第一站临床思维能力60分。',
              '1天搞定人机对话23分',
              '同学你可以不来，你的对手一定会来'
            ],
            image: [
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16a1174590758827968750_lc1.jpeg',
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/151c174590760508117773_lc2.jpeg',
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/6a3d174675877539598208_tihuan2.png'
            ],
            price: '99-399'
          }
        ],
        43: [
          {
            info: [
              '中医小黑屋',
              '技能最后一课，拿下40分',
              '第一站病例分析专项讲解',
              '精选往年真题+高频考题',
              '1天掌握诊断思路+方剂治法'
            ],
            image: [
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/671f174590744383184283_zy1.jpeg',
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/816e174675839885864251_tihuan.jpg',
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/91fb174590752339334452_zy3.jpeg'
            ],
            price: '99-199'
          }
        ],
        44: [
          {
            info: [
              // '中医小黑屋',
              '技能最后一课，拿下40分',
              '第一站病例分析专项讲解',
              '精选往年真题+高频考题',
              '1天掌握诊断思路+方剂治法'
            ],
            image: [
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/671f174590744383184283_zy1.jpeg',
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/816e174675839885864251_tihuan.jpg',
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/91fb174590752339334452_zy3.jpeg'
            ],
            price: '99-199'
          }
        ]
      },
      evaluationData: {
        45: [
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/6618174582023704291272_kq1.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1a26174582024682842495_kq2.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1442174582025632842150_kq3.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1b33174582026518834829_kq4.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/25e1174582027382294681_kq5.png'
          }
        ],
        42: [
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/3b90174582029412351337_lc1.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/d71c174582031144817414_lc2.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/5059174582031973591495_lc3.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4f79174590729969629860_lc4.png'
          }
        ],
        43: [
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/81ab174582033165314795_zy1.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/83de174582034371961805_zy2.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4cbb174582036355471021_zy3.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/fcc217458203731407265_zy4.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/3e8f17458203825285857_zy5.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/47d7174590733303287150_zy6.png'
          }
        ],
        44: [
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/81ab174582033165314795_zy1.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/83de174582034371961805_zy2.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4cbb174582036355471021_zy3.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/fcc217458203731407265_zy4.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/3e8f17458203825285857_zy5.png'
          },
          {
            imgUrl:
              'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/47d7174590733303287150_zy6.png'
          }
        ]
      },
      cityData: [
        '陕西校区',
        '湖南校区',
        '山东校区',
        '河南校区',
        '河北校区',
        '吉林校区',
        '辽宁校区',
        '广东校区',
        '江苏校区',
        '安徽校区',
        '广西校区',
        '甘肃校区',
        '浙江校区',
        '四川校区',
        '贵州校区',
        '湖北校区',
        '山西校区',
        '重庆校区',
        '云南校区',
        '新疆校区',
        '福建校区',
        '江西校区',
        // '京津冀校',
        '保定校区',
        '天津校区',
        '北京校区',
        '南阳校区',
        '黑龙江校',
        '内蒙校区'
      ],
      cityType: false,
      cityIdx: undefined,
      mType: false,
      webViewUrl: '',
      cityValue: '',
      yuyueType: false
    }
  },
  onLoad(e) {
    console.log(e, 'e3')
    if (e.scene) {
      this.$store.commit('jintiku/setXyppid', e.scene)
    }
  },
  onShow(e) {
    if (this.isLogin()) {
      uni.request({
        url: 'https://api.jinyingjie.com/ApiZt250501/getUid',
        method: 'GET', // 请求方法
        data: {
          phone: uni.getStorageSync('__xingyun_userPhone__')
        },
        success: res => {
          if (res.data.entity.uid) {
            uni.setStorageSync('__UID__', res.data.entity.uid)
            setTimeout(() => {
              if (uni.getStorageSync('__YUYUE_TYPE__')) {
                this.yuyueType = true
              }
              this.user_id = uni.getStorageSync('__UID__')
              this.getData()
            }, 30)
          } else {
            uni.showToast({
              title: '未获取到uid'
            })
            this.getData()
          }
        },
        fail: err => {
          console.log('GET 请求失败', err)
        }
      })
    } else {
      this.getData()
    }
  },
  methods: {
    goVideo(item) {
      const url = `https://m.jinyingjie.com/Live/detail2019s?id=${item.id}&setuid=666666${this.user_id}666666`
      this.webViewUrl = url
      this.mType = true
    },
    submit1() {
      wx.requestSubscribeMessage({
        tmplIds: ['PS9wWvqZkhqF_o-5ov6KqGfx67yDUJ0AetG00gnX4Ic'],
        success: res => {
          this.submit()
        },
        fail: err => {}
      })
    },
    async submit() {
      if (!this.cityValue) {
        uni.showToast({
          title: '请选择城市'
        })
        return
      }
      const res = await cityRemark({
        remark: this.cityValue
      })
      if (res.code == 100000) {
        this.cityType = false
        uni.showToast({
          title: '预约成功'
        })
        this.yuyueType = true
        uni.setStorageSync('__YUYUE_TYPE__', '1')
        setTimeout(() => {
          uni.switchTab({
            url: '/modules/jintiku/pages/index/index',
            fail(err) {
              console.log(err)
            }
          })
        }, 1800)
      }
    },
    handleCity(item, idx) {
      this.cityIdx = idx
      this.cityValue = item
    },
    yuyueSuccess(item, idx) {
      this.yuyueVideo(item, idx)
    },
    yuyueVideo(item, idx) {
      // if (!this.isLogin()) {
      //   goToLogin('goToLogin44')
      //   return
      // }
      wx.requestSubscribeMessage({
        tmplIds: ['PS9wWvqZkhqF_o-5ov6KqGfx67yDUJ0AetG00gnX4Ic'],
        success: res => {
          console.log(res)
          uni.request({
            url: 'https://api.jinyingjie.com/ApiZt250501/clickAppoinmentLive',
            method: 'GET', // 请求方法
            data: {
              user_id: this.user_id,
              id: item.id
            },
            success: res => {
              //  console.log('GET 请求成功返回服务器时间', res.data.entity.list);
              //  this.allData = res.data.entity.list
              uni.showToast({
                title: '预约成功'
              })
              this.allData.free_live[idx].yu_yue_status = 1

              setTimeout(() => {
                uni.switchTab({
                  url: '/modules/jintiku/pages/index/index',
                  fail(err) {
                    console.log(err)
                  }
                })
              }, 1800)
            },
            fail: err => {
              console.log('GET 请求失败', err)
            }
          })
        },
        fail: err => {
          console.log(err, 'err')
          uni.request({
            url: 'https://api.jinyingjie.com/ApiZt250501/clickAppoinmentLive',
            method: 'GET', // 请求方法
            data: {
              user_id: this.user_id,
              id: item.id
            },
            success: res => {
              //  console.log('GET 请求成功返回服务器时间', res.data.entity.list);
              //  this.allData = res.data.entity.list
              uni.showToast({
                title: '预约成功'
              })
              this.allData.free_live[idx].yu_yue_status = 1

              setTimeout(() => {
                uni.switchTab({
                  url: '/modules/jintiku/pages/index/index',
                  fail(err) {
                    console.log(err)
                  }
                })
              }, 1800)
            },
            fail: err => {
              console.log('GET 请求失败', err)
            }
          })
        }
      })
    },
    orderMessage() {
      wx.requestSubscribeMessage({
        tmplIds: ['PS9wWvqZkhqF_o-5ov6KqGfx67yDUJ0AetG00gnX4Ic'],
        success: res => {},
        fail: err => {}
      })
    },
    xiaoheiwuYuyue() {
      // if (!this.isLogin()) {
      //   goToLogin('goToLogin44')
      //   return
      // }
      wx.requestSubscribeMessage({
        tmplIds: ['PS9wWvqZkhqF_o-5ov6KqGfx67yDUJ0AetG00gnX4Ic'],
        success: res => {
          this.cityType = true
        },
        fail: err => {
          console.log(err, 'err')
          this.cityType = true
        }
      })
    },
    handleTeacher(professionId, idx) {
      this.currentTeacherIdx = idx
      this.currentTeacher = this.teacherData[professionId][idx]
    },
    handleProfession(idx, id) {
      this.currentIdx = idx
      this.professionId = id
      this.currentTeacherIdx = 0
      this.currentTeacher = this.teacherData[id][0]
      this.getData()
    },
    isLogin() {
      return !!this.$store.state.jintiku.token
    },
    async getData() {
      uni.request({
        url: 'https://api.jinyingjie.com/ApiZt250501/getListAll',
        method: 'GET', // 请求方法
        data: {
          user_id: this.user_id,
          profession_id: this.professionId
        },
        success: res => {
          console.log('GET 请求成功返回服务器时间', res.data.entity.list)
          this.allData = res.data.entity.list
        },
        fail: err => {
          console.log('GET 请求失败', err)
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.skill-black-room {
  background: #fef1e3;
  height: 100%;
  .banner {
    width: 750rpx;
    height: 380rpx;
    background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/960f174572062398835116_banner.png');
    background-size: 100%;
  }

  .switch {
    width: 710rpx;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    position: sticky;
    z-index: 30;
    top: 5%;
    .switch-item {
      width: 154rpx;
      height: 45rpx;
      background: linear-gradient(-75deg, #ffe8be, #ffbc72);
      border-radius: 23rpx;
      border: 1px solid #e9ae87;
      font-weight: 500;
      font-size: 22rpx;
      color: #bb410a;
      line-height: 45rpx;
      text-align: center;
    }
    .actived {
      background: #ff6817;
      color: #ffffff;
    }
  }

  .module {
    width: 712rpx;
    margin: 0 auto;
    .top {
      width: 712rpx;
      height: 90rpx;
      margin-top: 21rpx;
      background-image: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/9862174572261216731975_top.png');
      background-size: 100%;
      display: flex;
      .left-title {
        width: 240rpx;
        font-weight: bold;
        font-size: 40rpx;
        color: #ffffff;
        line-height: 80rpx;
        margin-left: 30rpx;
        text-shadow: 2rpx 3rpx 0rpx #9c4800;
      }
      .right-title {
        font-weight: 500;
        font-size: 30rpx;
        color: #691400;
        line-height: 80rpx;
        .small-title {
          line-height: 40rpx;
          text {
            font-size: 30rpx;
          }
        }
      }
    }
    .center {
      overflow: hidden;
      width: 100%;
      background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1340174572291182036020_center.png');
      background-size: 100%;
      .video {
        .swiper1 {
          height: 490rpx;
        }
        .swiper-item-box {
          height: 490rpx;
        }
        .video-item {
          width: 587rpx;
          background: #ffffff;
          box-shadow: 0rpx 10rpx 10rpx 0rpx rgba(237, 216, 178, 0.53);
          border-radius: 20rpx;
          margin: 0 auto;
          padding-bottom: 40rpx;
          .video-image {
            margin: 11rpx auto;
            display: block;
            width: 565rpx;
            height: 260rpx;
            background: #bd5a05;
            border-radius: 20rpx 20rpx 0rpx 0rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
          }
          .video-name {
            font-size: 24rpx;
            color: #b46000;
            margin: 0 20rpx;
          }
          .video-teacher {
            font-weight: 500;
            font-size: 22rpx;
            color: #b46000;
            margin: 0 22rpx;
          }
          .actions {
            font-weight: 400;
            font-size: 22rpx;
            color: #b46000;
            margin: 0 20rpx;
            display: flex;
            justify-content: space-between;
            .left {
              display: flex;
              align-items: flex-end;
              .type {
                width: 90rpx;
                height: 34rpx;
                background: #fcca9e;
                border-radius: 4rpx;
                text-align: center;
                font-weight: 500;
                font-size: 22rpx;
                color: #cb0d10;
                margin-left: 10rpx;
              }
            }
            .right {
              width: 189rpx;
              height: 57rpx;
              background: linear-gradient(0deg, #ff6816, #ff8339);
              border-radius: 28rpx;
              font-weight: 500;
              font-size: 28rpx;
              color: #ffffff;
              line-height: 57rpx;
              text-align: center;
            }
            .over {
              background: #999;
            }
          }
        }
      }
    }

    .bottom {
      width: 100%;
      height: 40rpx;
      display: 712rpx;
    }
  }
  .module2 {
    .info {
      margin: 0 auto;
      width: 558rpx;
      .info-item {
        width: 558rpx;
        height: 54rpx;
        background: linear-gradient(0deg, #fef0e0, #ffffff);
        border-radius: 27rpx;
        border: 1px solid #ffd0a8;
        font-size: 24rpx;
        color: #333333;
        line-height: 54rpx;
        margin-bottom: 9rpx;
        display: flex;
        align-items: center;
        .ok {
          width: 34rpx;
          height: 29rpx;
          background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/8224174581454916315991_%E7%BB%84%20379.png');
          background-size: 100%;
          margin: 0 12rpx;
        }
      }
    }
    .image2 {
      margin: 24rpx auto 0;
      width: 628rpx;
      .swiper-image2 {
        height: 330rpx;
        image {
          height: 100%;
        }
      }
    }
    .details {
      width: 600rpx;
      margin: 25rpx auto 0;
      display: flex;
      justify-content: center;
      .prize {
        font-weight: bold;
        font-size: 30rpx;
        color: #c90001;
        background: linear-gradient(0deg, #c6271a 0%, #f55100 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        display: flex;
        align-items: center;
        .text {
          font-size: 30rpx;
        }
        margin-right: 30rpx;
      }
      .btn {
        width: 189rpx;
        height: 57rpx;
        background: linear-gradient(0deg, #ff6816, #ff8339);
        border-radius: 28rpx;
        font-weight: 500;
        font-size: 28rpx;
        color: #ffffff;
        line-height: 57rpx;
        text-align: center;
      }
      .over {
        background: #999;
      }
    }
  }
  .module3 {
    .teacher {
      .teacher-item {
        .teacher-bj {
          width: 478rpx;
          height: 600rpx;
          background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/2552174573795919782735_bj.png');
          margin: 0 auto;
          background-repeat: no-repeat;
          background-size: 100%;
          image {
            width: 479rpx;
            height: 580rpx;
          }
        }
        .info {
          margin: 60rpx;
          .name {
            display: flex;
            .name2 {
              width: 74rpx;
              height: 35rpx;
              background: url(https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4b92174573875863421141_%E7%9F%A9%E5%BD%A2%20986.png);
              background-size: 100%;
              font-size: 22rpx;
              color: #000000;
              line-height: 35rpx;
              text-align: center;
            }

            font-weight: bold;
            font-size: 35rpx;
            color: #000000;
            line-height: 38rpx;
          }
          .title {
            font-weight: bold;
            font-size: 24rpx;
            color: #333333;
            margin-top: 28rpx;
          }
          .content {
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            line-height: 32rpx;
          }
        }
      }
      .teacher-boxs {
        width: 715rpx;
        display: flex;
        margin: 0 auto;
        overflow-x: scroll;
        .teacher-box {
          flex-shrink: 0;
          width: 220rpx;
          height: 168rpx;
          background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/2a9e174582251266946456_123.png');
          background-repeat: no-repeat;
          background-size: 218rpx 108rpx;
          background-position: bottom;
          text-align: center;
          margin-right: 4rpx;
          image {
            width: 137rpx;
            height: 168rpx;
          }
        }
        .actived {
          background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/7d79174573865640914691_%E7%BB%84%20379%20(1).png');
          background-repeat: no-repeat;
          background-size: 218rpx 108rpx;
          background-position: bottom;
        }
      }
    }
  }

  .module4 {
    .center {
      padding: 60rpx 0;
    }
  }

  .module5 {
    .news {
      margin-top: 20rpx;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      width: 660rpx;
      margin: 0 auto;
      .news-item {
        width: 319rpx;
        height: 204rpx;
        margin-bottom: 12rpx;
      }
    }
    .news-content {
      font-size: 22rpx;
      color: #333333;
      line-height: 30rpx;
      margin: 20rpx 50rpx;
      height: 360rpx;
      overflow: hidden;
      &.active {
        height: auto;
      }
    }
    .btnzk {
      text-align: center;
      font-size: 24rpx;
      color: #2e68ff;
      display: flex;
      align-items: center;
      justify-content: center;
      image {
        width: 30rpx;
        height: 30rpx;
        transition: all 0.25s;
      }
    }
    .active {
      image {
        transform: rotateZ(180deg);
      }
    }
  }

  .code {
    width: 750rpx;
    height: 773rpx;
    background: #bd5a05;
    display: flex;
    justify-content: space-around;
    padding-top: 95rpx;
    flex-wrap: wrap;
    .code-item {
      width: 226rpx;
      margin: 0 50rpx;
      height: 300rpx;
      image {
        width: 226rpx;
        height: 226rpx;
      }
      .name {
        font-weight: 500;
        font-size: 24rpx;
        color: #ffffff;
        line-height: 32rpx;
        text-align: center;
      }
      .name2 {
        font-weight: 400;
        font-size: 18rpx;
        color: #ffffff;
        line-height: 32rpx;
        text-align: center;
      }
    }
  }

  .advert {
    width: 650rpx;
    height: 60rpx;
    background: linear-gradient(0deg, #ff6816, #ff8339);
    border-radius: 30rpx;
    bottom: 23rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    position: sticky;
    left: 50rpx;
    // transform: translate(-50%);
    bottom: 2%;
    z-index: 20;
    .text {
      font-weight: 400;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 60rpx;
      text-shadow: 0rpx 1rpx 6rpx #cb4829;
      margin-right: 40rpx;
    }
    .btn {
      width: 143rpx;
      height: 35rpx;
      background: linear-gradient(0deg, #ffeb7f, #ffffff);
      box-shadow: 0rpx 1rpx 6rpx 0rpx #c34d31;
      border-radius: 18rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #ff7426;
      line-height: 35rpx;
      text-align: center;
    }
    // .over {
    //     background: #999;
    //     color: #fff;
    // }
  }

  .dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 50;
    .main {
      position: relative;
      width: 710rpx;
      height: 1050rpx;
      background: #ffffff;
      border-radius: 10rpx;
      border: 1px solid #bd5a05;
      margin: 20% auto;
      overflow: hidden;
      .title {
        font-size: 32rpx;
        color: #000000;
        margin: 52rpx auto 32rpx;
        text-align: center;
      }
      .citys {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-evenly;
        .city-item {
          width: 193rpx;
          height: 56rpx;
          background: #ffffff;
          border-radius: 28rpx;
          border: 1px solid #ff6817;
          font-size: 26rpx;
          color: #ff6817;
          line-height: 56rpx;
          text-align: center;
          margin-bottom: 27rpx;
        }
        .actived {
          width: 193rpx;
          height: 56rpx;
          background: #ff6817;
          border-radius: 28rpx;
          color: #ffffff;
        }
      }
      .submit {
        width: 330rpx;
        height: 56rpx;
        background: linear-gradient(0deg, #ff6816, #ff8339);
        border-radius: 28rpx;
        font-size: 30rpx;
        color: #ffffff;
        line-height: 56rpx;
        text-align: center;
        position: absolute;
        bottom: 40rpx;
        left: 50%;
        transform: translate(-50%);
      }
    }
  }

  .h5 {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 100;
  }
}
</style>
