<template>
  <view class="lessons-list">
    <view class="lessons-title">
      <view class="left">
        <img style="margin-right: 10rpx; width: 25rpx;height: 25rpx;"
          src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/title-icon.png" alt="" />
        <view class="today">
          今日共
          <text style="color: #FF860E;">{{ info.lesson_num || 0 }}</text>
          节课
        </view>
      </view>
      <view class="accomplish">
        已完成
        <text style="color: #000000;">{{ info.lesson_attendance_num || 0 }}</text>
        节
      </view>
    </view>
    <view class="lessons-list-box">
      <view v-for="(item, index) in info.lesson_attendance" :key="index" class="today-lesson-item">

        <view class="today-lesson-item-left">
          <view class="start-time">
            {{ getStartTime(item.start_time) }}
          </view>
          <view class="class-type">
            <view v-if="item.teaching_type == 1" class="today-lesson-type-status">
              直播
            </view>
            <view v-if="item.teaching_type == 3" class="today-lesson-type-status">
              录播
            </view>
            <view v-if="item.teaching_type == 2" class="today-lesson-type-status">
              面授
            </view>
          </view>
        </view>
        <view class="today-lesson-item-right">
          <!--    进行中    -->
          <!-- <image
	  	    v-if="item.lesson_status == '2'"
	  	    class="today-lesson-item-status"
	  	    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4aab173314138348667828_jinxingzhong.png"
	  	  /> -->
          <!--    未开始    -->
          <!-- <image
	  	    v-if="item.lesson_status == '1'"
	  	    class="today-lesson-item-status"
	  	    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/235d173296054838499208_%E6%9C%AA%E5%BC%80%E5%A7%8B.png"
	  	  /> -->
          <!--    已结束    -->
          <!-- <image
	  	    v-if="item.lesson_status == '3'"
	  	    class="today-lesson-item-status"
	  	    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/7808173528279509229726_jeishu.png"
	  	  /> -->

          <view class="today-lesson-title" @click="goLookCourse(item)">
            {{ item.lesson_name }}
          </view>
          <view class="learn-course-lessons-assistant-name-name">
            {{ item.lesson_name_other?item.lesson_name_other:'副标题' }}
          </view>
		  <view class="button-group">
		  	<view class="button-group-child full-class">
		  		课前作业
		  	</view>
			<view class="button-group-child">
				资料
			</view>
			<view class="button-group-child">
				评价
			</view>
		  </view>
		  
        </view>
      </view>

    </view>
  </view>
</template>
<script>
import { liveUrl } from '../../api/index'

export default {
  name: 'ModuleStudyLessonsList',
  props: {
    info: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    getStartTime(time) {
      if (time) {
        return time.toString().split(' ')[1];
      }
    },
    // 切换展开/收起
    toggleOpen(item) {
      this.$set(item, 'isOpen', !item.isOpen)
    },
    // 拼接完整路径
    completePath(path) {
      // TODO: 需要配置OSS基础URL
      return 'https://ysys-assets.oss-cn-beijing.aliyuncs.com' + path;
    },

    goLookCourse(item) {
      if (item.teaching_type != "1") {
        return;
      }
      liveUrl({
        lesson_id: item.lesson_id,
      })
        .then(({ data }) => {
          if (data && data.playback_url) {
            this.$xh.push('jintiku', `pages/study/video/index?url=${encodeURIComponent(data.playback_url)}&filter_goods_id=${data.goods_id || ""}&order_id=${item.order_id || ""}&goods_id=${item.goods_id || ""}&goods_pid=${item.goods_pid || ""}&lesson_id=${item.lesson_id}`);
          } else {
            this.$xh.push('jintiku', `pages/study/live/index?lesson_id=${item.lesson_id}&url=${encodeURIComponent(data.live_url)}`);
          }
        })
        .catch(() => {
          uni.showToast({
            title: "获取直播地址失败！",
            icon: 'none'
          });
        });
    },
    goDataDownload(url) {
      if (!url) {
        uni.showToast({
          title: "暂无资料",
          icon: 'none'
        });
        return;
      }
      // TODO: 实现文件预览功能
      this.$xh.push('jintiku', `pages/study/pdf/index?pdfUrl=${encodeURIComponent(this.completePath(url))}`);
    },

    // 跳转答题
    goAnswer(item, btn) {
      console.log(item, "item");
      console.log(btn, "btn");
      this.$xh.push('jintiku', `pages/answertest/answer/index?paper_version_id=${btn.paper_version_id || ""}&evaluation_type_id=${btn.id}&professional_id=${btn.professional_id || ""}&goods_id=${item.paper_goods_id}&order_id=${item.order_id}&system_id=${item.system_id}&order_detail_id=${item.order_goods_detail_id}&lesson_id=${item.lesson_id || ""}`);
    }
  }
}
</script>
<style lang="less" scoped>
.lessons-list {
  box-sizing: border-box;
  width: 100%;
  margin: 20px 0px 0px 0px;
  background-color: white;
  padding: 32rpx 30rpx;

  .lessons-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .left {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      font-size: 15px;
      color: #262629;

      .line {
        width: 4px;
        height: 16px;
        margin-right: 6px;
        background: #018CFF;
        border-radius: 3px;
      }

      .today {
        font-weight: 600;

        span {
          font-weight: 600;
          color: #018CFF;
        }
      }
    }

    .accomplish {
      font-size: 13px;
      color: #666666;
      line-height: 18px;
      margin-left: 26rpx;

      span {
        color: #018CFF;
        font-weight: 600;
      }
    }
  }

  .today-lesson-item-left {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
	align-items: center;
    padding: 0rpx 24rpx;
	width: 30%;

    .start-time {
		margin-top: 30rpx;
      color: #009F32;
      font-size: 32rpx;
      font-weight: 700;
    }

    .today-lesson-type-status {
      text-align: center;
      border: 3rpx solid #0F4921;
      border-radius: 4px;
      font-weight: 400;
      font-size: 16px;
      color: #0F4921;
      padding: 3rpx 16rpx;
      margin-top: 12rpx;
	  width: 100rpx;
    }



  }
  
  .today-lesson-item-right{
	  display: flex;
	  flex-direction: column;
	  padding-left: 18rpx;
	  padding-top: 32rpx;
	  width: 70%;
	  
	  .button-group{
		  display: flex;
		  flex-direction: row;
		  justify-content: flex-end;
		  align-items: center;
		  margin-top: 32rpx;
	  }
	  
	  .button-group-child{
		  height: 60rpx;
		  padding: 0rpx 24rpx;
		  font-size: 22rpx;
		  font-weight: 600;
		  color: #FF860E;
		  border: 2rpx solid #FF860E;
		  border-radius: 30rpx;
		  display: flex;
		  flex-direction: row;
		  align-items: center;
		  justify-content: center;
		  margin-right: 24rpx;
	  }
	  
	  .full-class{
		  background: linear-gradient(90deg,
					#FF860E 0%,
					#FF6912 100%,
					rgba(255, 255, 255, 0) 100%);
		  color:white;
		  border: 0rpx;
	  }
  }




  .lessons-list-box {
    .today-lesson-item {
      background: #F4F9FF;
      margin-bottom: 10px;
      border-radius: 32rpx;
	  padding: 24rpx 0rpx;
	  padding-left: 15px;
      position: relative;
      display: flex;
      flex-direction: row;
	  min-height: 200rpx;

      .today-lesson-item-status {
        position: absolute;
        right: 0;
        top: 0;
        width: 55px;
        height: 22px;
      }

      .today-lesson-top {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 13px;

        .today-lesson-type {}

        .today-lesson-time {
          font-weight: 400;
          font-size: 13px;
          color: #424b57;
        }
      }

      .today-lesson-title {
        font-weight: 500;
        font-size: 15px;
        color: #262629;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        word-break: break-all;
        line-height: 1.4;
        max-height: calc(1.4em * 2);
      }

      .learn-course-lessons-assistant-name-name {
        color: #666666;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 350rpx;
        box-sizing: border-box;
      }


      .learn-course-lessons-assistant-name {
        margin-top: 11px;
        font-size: 13px;
        //margin-bottom: 10px;
        line-height: 16px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .learn-course-lessons-assistant-name-operate {
          width: 30px;
          text-align: right;
          color: #018CFF;
        }

        .learn-course-lessons-assistant-name-show {
          overflow: auto;
          text-overflow: clip;
          white-space: normal;
        }
      }

      .today-lesson-from {
        border-radius: 4px;
        font-weight: 400;
        font-size: 11px;
        color: #7e4e0c;
        margin-top: 10px;
        height: 28px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        background: #fff7ec;
        padding-left: 6px;

        .label {
          width: 32px;
          height: 16px;
          line-height: 16px;
          background: #fddfb5;
          text-align: center;
          border-radius: 8px;
          margin-right: 4px;
          font-weight: 400;
          font-size: 10px;
          color: #7e4e0c;
        }

        //padding-bottom: 20px;
      }

      .today-lesson-operation {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        text-align: center;
        padding-top: 22px;

        .operation-box {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          font-weight: 400;
          font-size: 12px;
          color: #424b57;
          padding-bottom: 22px;

          .operation-box-item {
            display: flex;
            justify-content: flex-start;
            align-items: center;
          }

          .operation-box-item:not(:last-child) {
            position: relative;
            padding-right: 24px;
            margin-right: 24px;
          }

          .operation-box-item:not(:last-child)::after {
            content: "";
            width: 1px;
            height: 10px;
            background: #eceff1;
            position: absolute;
            right: 0;
            top: 2px;
          }

          .operation-img {
            width: 14px;
            height: 14px;
            margin-right: 3px;
          }

          .operation-text {
            color: #424b57;
          }
        }

        .independent-list {
          padding-top: 10px;

          .independent-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            //border-top: 1px solid #E8E9EA;

            .independent-list-item-title {
              display: flex;
              justify-content: flex-start;
              align-items: center;

              .independent-list-item-title-img {
                width: 24px;
                height: 24px;
              }

              .independent-list-item-title-name {
                margin-left: 8px;
                font-weight: 600;
                font-size: 15px;
                color: #262629;
              }
            }

            .independent-list-item-operate {
              display: flex;
              justify-content: center;
              align-items: center;
              font-weight: 400;
              font-size: 12px;
              color: #93969f;
            }

            .go-exam {
              margin-right: 0px;
              font-weight: 400;
              font-size: 12px;
              color: #018CFF;
              width: 72px;
              height: 28px;
              border-radius: 14px;
              border: 1px solid rgba(1, 163, 99, 0.56);
              text-align: center;
              line-height: 26px;
            }

            .over {
              margin-right: 0px;
              font-weight: 400;
              font-size: 12px;
              width: 72px;
              height: 28px;
              border-radius: 14px;
              border: 1px solid #e2e2e2;
              text-align: center;
              line-height: 26px;
            }

            .unlock {
              display: flex;
              justify-content: center;
              align-items: center;
              margin-right: 0px;
              font-weight: 400;
              font-size: 12px;
              color: #969696;
              width: 72px;
              height: 28px;
              border-radius: 14px;
              border: 1px solid transparent;
              background: #efefef;

              img {
                width: 14px;
                height: 14px;
                margin-right: 2px;
              }
            }
          }

          .independent-item:last-child {
            padding-bottom: 5px;
          }

          //.independent-item:first-child {
          //  margin-top: 14px;
          //}
        }
      }
    }

    .today-lesson-item:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
